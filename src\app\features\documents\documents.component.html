<div class="documents-container">
  <!-- Breadcrumb -->
  <app-breadcrumb 
    [breadcrumbs]="breadcrumbItems"
    [size]="breadcrumbSizeEnum.Medium" 
    divider=">"
    (breadcrumbClicked)="onBreadcrumbClicked($event)">
  </app-breadcrumb>

  <!-- Page Header -->
  <div class="page-header mb-4">
    <div class="d-flex justify-content-between align-items-center">
      <h2 class="page-title">{{ 'DOCUMENTS.TITLE' | translate }}</h2>
      
      <!-- Upload Button -->
      <app-custom-button
        *ngIf="canUploadDocuments()"
        [btnName]="'DOCUMENTS.UPLOAD_DOCUMENT' | translate"
        (click)="onUploadDocument()"
        [buttonType]="buttonEnum.Primary"
        [iconName]="iconEnum.add">
      </app-custom-button>
    </div>
    
    <p class="page-description">{{ 'DOCUMENTS.DESCRIPTION' | translate }}</p>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner></mat-spinner>
  </div>

  <!-- Document Tabs -->
  <div *ngIf="!isLoading" class="documents-content">
    <mat-tab-group 
      [(selectedIndex)]="selectedTabIndex"
      (selectedTabChange)="onTabChange($event.index)"
      class="documents-tabs">
      
      <mat-tab 
        *ngFor="let category of documentCategories" 
        [label]="category.label | translate">
        
        <!-- Document List Component -->
        <app-document-list
          [category]="category"
          [fundId]="currentFundId">
        </app-document-list>
        
      </mat-tab>
    </mat-tab-group>
  </div>
</div>
